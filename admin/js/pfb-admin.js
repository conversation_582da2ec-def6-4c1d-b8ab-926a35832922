/**
 * Admin JavaScript for the Price Form Builder plugin.
 *
 * @since      1.0.0
 */
(function($) {
    'use strict';

    /**
     * Initialize the admin functionality.
     */
    function init() {
        console.log('PFB Admin: Initializing...');

        // Check if pfb_data is available
        if (typeof pfb_data === 'undefined') {
            console.error('PFB Admin: pfb_data is not defined! AJAX will not work.');
            return;
        }

        console.log('PFB Admin: pfb_data is available:', pfb_data);
        console.log('PFB Admin: pfb_data available:', typeof pfb_data !== 'undefined');

        initTabs();
        initFormBuilder();
        initPriceVariables();
        initCurrencies();
        initSettings();

        console.log('PFB Admin: Initialization complete');
    }

    /**
     * Initialize tabs functionality.
     */
    function initTabs() {
        $('.pfb-tab').on('click', function() {
            const tabId = $(this).data('tab');

            // Update active tab
            $('.pfb-tab').removeClass('active');
            $(this).addClass('active');

            // Show active tab content
            $('.pfb-tab-content').removeClass('active');
            $('#' + tabId).addClass('active');
        });
    }

    /**
     * Initialize form builder functionality.
     */
    function initFormBuilder() {
        console.log('PFB Admin: Initializing form builder...');
        console.log('PFB Admin: Form builder element found:', $('#pfb-form-builder').length > 0);

        if (!$('#pfb-form-builder').length) {
            console.log('PFB Admin: No form builder element found, skipping initialization');
            return;
        }

        console.log('PFB Admin: Setting up field button click handlers...');
        console.log('PFB Admin: Field buttons found:', $('.pfb-add-field-btn').length);

        // Debug button properties
        $('.pfb-add-field-btn').each(function(index) {
            console.log('Button ' + index + ':', {
                text: $(this).text().trim(),
                type: $(this).data('type'),
                classes: $(this).attr('class'),
                disabled: $(this).prop('disabled'),
                visible: $(this).is(':visible'),
                offset: $(this).offset()
            });
        });

        // Test direct click handler first
        $('.pfb-add-field-btn').on('click', function(e) {
            console.log('PFB Admin: Direct click handler fired!', $(this).data('type'));
            console.log('Event details:', e);
            e.preventDefault();
            e.stopPropagation();

            // Try to add field directly
            const fieldType = $(this).data('type');
            if (fieldType) {
                addField(fieldType);
            }
        });

        // Add field button click (delegated)
        $(document).on('click', '.pfb-add-field-btn', function(e) {
            console.log('PFB Admin: Delegated click handler fired!', $(this).data('type'));
            const fieldType = $(this).data('type');
            const fieldId = 'field_' + Date.now();

            // Create new field
            const $field = createField(fieldType, fieldId);

            // Add to form fields
            $('.pfb-form-fields').append($field);

            // Hide empty message if it's visible
            $('.pfb-empty-form-message').hide();

            // Open field settings
            $('#' + fieldId + ' .pfb-field-edit').trigger('click');

            // Scroll to the new field
            $('html, body').animate({
                scrollTop: $field.offset().top - 100
            }, 300);
        });

        // Field actions
        $(document).on('click', '.pfb-field-edit', function() {
            const $field = $(this).closest('.pfb-field');
            const fieldId = $field.attr('id');

            // Debug log for field edit
            console.log('Editing field with id:', fieldId);

            toggleFieldSettings(fieldId);

            // Debug log for width dropdown after toggling settings
            setTimeout(function() {
                console.log('Width dropdown exists after toggle:', $field.find('.pfb-field-width-input').length > 0);
                console.log('Width dropdown value:', $field.find('.pfb-field-width-input').val());
            }, 100);
        });

        $(document).on('click', '.pfb-field-duplicate', function() {
            const $field = $(this).closest('.pfb-field');
            const $clone = $field.clone(true);
            const newId = 'field_' + Date.now();

            $clone.attr('id', newId);
            $clone.find('.pfb-field-settings').removeClass('active');

            $field.after($clone);
        });

        // Toggle hidden field options when the hidden checkbox is changed
        $(document).on('change', '.pfb-field-hidden-input', function() {
            const $field = $(this).closest('.pfb-field');
            const $hiddenOptions = $field.find('.pfb-hidden-field-options');
            const isChecked = $(this).is(':checked');

            console.log('Hidden checkbox changed:', isChecked);

            if (isChecked) {
                $hiddenOptions.slideDown(200);
            } else {
                $hiddenOptions.slideUp(200);
            }
        });

        // Toggle conditional logic settings when the checkbox is changed
        $(document).on('change', '.pfb-enable-conditional-logic', function() {
            const $field = $(this).closest('.pfb-field');
            const $conditionalSettings = $field.find('.pfb-conditional-logic-settings');
            const isChecked = $(this).is(':checked');

            console.log('Conditional logic checkbox changed:', isChecked);

            if (isChecked) {
                $conditionalSettings.slideDown(200);
                // Load available fields for conditions
                loadConditionalFields($field);
            } else {
                $conditionalSettings.slideUp(200);
            }
        });

        // Add new condition
        $(document).on('click', '.pfb-add-condition', function() {
            const $condition = $(this).closest('.pfb-conditional-condition');
            const $newCondition = $condition.clone();

            // Clear values in the new condition
            $newCondition.find('select, input').val('');

            // Change the add button to remove button
            $newCondition.find('.pfb-add-condition')
                .removeClass('pfb-add-condition')
                .addClass('pfb-remove-condition')
                .text('-');

            $condition.after($newCondition);

            // Load fields for the new condition
            const $field = $(this).closest('.pfb-field');
            loadConditionalFields($field);
        });

        // Remove condition
        $(document).on('click', '.pfb-remove-condition', function() {
            $(this).closest('.pfb-conditional-condition').remove();
        });

        // Open variable selector for hidden fields
        $(document).on('click', '.pfb-select-hidden-variable', function() {
            const $button = $(this);
            const $option = $button.closest('.pfb-option-value-container');
            const $valueInput = $option.find('.pfb-hidden-field-value');
            const $variableInput = $option.find('.pfb-hidden-field-variable');

            console.log('Variable selector clicked, inputs:', {
                valueInput: $valueInput.length ? $valueInput.attr('id') : 'not found',
                variableInput: $variableInput.length ? $variableInput.attr('id') : 'not found'
            });

            // Store references to the inputs
            $('#pfb-dynamic-value-modal').data('valueInput', $valueInput);
            $('#pfb-dynamic-value-modal').data('variableInput', $variableInput);

            // Load variables
            loadDynamicValues();

            // Show modal
            $('#pfb-dynamic-value-modal').show();
        });

        // Save field settings
        $(document).on('click', '.pfb-save-field-settings', function() {
            const $field = $(this).closest('.pfb-field');
            const fieldType = $field.data('type');

            // Update field title with the label
            const fieldLabel = $field.find('.pfb-field-label-input').val();
            $field.find('.pfb-field-title').text(fieldLabel);

            // Get field width
            const fieldWidth = $field.find('.pfb-field-width-input').val() || '100';
            console.log('SAVE: Field width set to:', fieldWidth, 'for field:', $field.find('.pfb-field-name-input').val());
            console.log('SAVE: Width dropdown value:', $field.find('.pfb-field-width-input').val());
            console.log('SAVE: Width dropdown exists:', $field.find('.pfb-field-width-input').length > 0);

            // Store the width as a data attribute on the field
            $field.attr('data-width', fieldWidth);

            // Remove any existing width indicator
            $field.find('.pfb-field-width-indicator').remove();

            // Add a visual indicator of the field width
            if (fieldWidth !== '100') {
                $field.find('.pfb-field-title').after(`<span class="pfb-field-width-indicator">Width: ${fieldWidth}%</span>`);
            }

            // Handle conditional logic
            const hasConditionalLogic = $field.find('.pfb-enable-conditional-logic').is(':checked');
            if (hasConditionalLogic) {
                // Add visual indicator for conditional logic
                $field.find('.pfb-field-width-indicator').remove();
                $field.find('.pfb-field-title').after(`<span class="pfb-field-conditional-indicator">Conditional</span>`);
            } else {
                $field.find('.pfb-field-conditional-indicator').remove();
            }

            // For dropdown, radio, and checkbox fields, ensure options are properly set
            if (['dropdown', 'radio', 'checkbox'].includes(fieldType)) {
                // Log options for debugging
                console.log('Field options before saving:', getFieldOptions($field));

                // Update field preview
                updateFieldPreview($field);
            }

            // Close settings panel
            $field.find('.pfb-field-settings').removeClass('active');
        });

        // Cancel field settings
        $(document).on('click', '.pfb-cancel-field-settings', function() {
            const $field = $(this).closest('.pfb-field');
            $field.find('.pfb-field-settings').removeClass('active');
        });

        // Add option
        $(document).on('click', '.pfb-add-option', function() {
            const $option = $(this).closest('.pfb-field-option');
            const $newOption = $option.clone();

            // Clear values
            $newOption.find('input').val('');

            // Change button from add to remove
            $newOption.find('.pfb-add-option')
                .removeClass('pfb-add-option')
                .addClass('pfb-remove-option')
                .text('-');

            $option.after($newOption);
        });

        // Add subtotal line
        $(document).on('click', '.pfb-add-subtotal-line', function() {
            const $line = $(this).closest('.pfb-subtotal-line');
            const $newLine = $line.clone();

            // Clear formula value but keep a default label
            $newLine.find('.pfb-subtotal-line-formula').val('');

            // Update the line label to be the next number
            const currentLabel = $line.find('.pfb-subtotal-line-label').val();
            const labelParts = currentLabel.split(' ');
            const lineNumber = parseInt(labelParts[labelParts.length - 1]) || 1;
            $newLine.find('.pfb-subtotal-line-label').val(labelParts.slice(0, -1).join(' ') + ' ' + (lineNumber + 1));

            // Change button from add to remove
            $newLine.find('.pfb-add-subtotal-line')
                .removeClass('pfb-add-subtotal-line')
                .addClass('pfb-remove-subtotal-line')
                .text('-');

            $line.after($newLine);
        });

        // Remove subtotal line
        $(document).on('click', '.pfb-remove-subtotal-line', function() {
            $(this).closest('.pfb-subtotal-line').remove();
        });

        // Open formula builder for subtotal line
        $(document).on('click', '.pfb-select-subtotal-formula', function() {
            const $button = $(this);
            const $line = $button.closest('.pfb-subtotal-line');
            const $formulaInput = $line.find('.pfb-subtotal-line-formula');
            const $field = $button.closest('.pfb-field');
            const $formulaTools = $field.find('.pfb-formula-tools');

            // Add active class to the current line
            $('.pfb-subtotal-line').removeClass('active-line');
            $line.addClass('active-line');

            // Store reference to the formula input
            $field.data('currentFormulaInput', $formulaInput);

            // Load fields for the formula builder
            loadFormulaFields($field);

            // Toggle formula tools with animation
            if ($formulaTools.is(':visible')) {
                $formulaTools.slideUp(200);
            } else {
                // Position the formula tools below the current line
                const linePosition = $line.position().top;
                const lineHeight = $line.outerHeight();
                const linesContainer = $field.find('.pfb-subtotal-lines');
                const linesScrollTop = linesContainer.scrollTop();

                // Adjust the scroll position to show the formula tools
                linesContainer.animate({
                    scrollTop: linePosition + linesScrollTop - 50
                }, 200);

                $formulaTools.slideDown(200);
            }
        });

        // Open dynamic value modal
        $(document).on('click', '.pfb-select-dynamic-value', function() {
            const $button = $(this);
            const $option = $button.closest('.pfb-field-option');
            const $valueInput = $option.find('.pfb-option-value');
            const $variableInput = $option.find('.pfb-option-variable');

            // Store references to the inputs
            $('#pfb-dynamic-value-modal').data('valueInput', $valueInput);
            $('#pfb-dynamic-value-modal').data('variableInput', $variableInput);

            // Load variables
            loadDynamicValues();

            // Show modal
            $('#pfb-dynamic-value-modal').show();
        });

        // Remove option
        $(document).on('click', '.pfb-remove-option', function() {
            $(this).closest('.pfb-field-option').remove();
        });

        $(document).on('click', '.pfb-field-delete', function() {
            if (confirm(pfb_data.i18n.confirm_delete)) {
                $(this).closest('.pfb-field').remove();

                // Show empty message if no fields left
                if ($('.pfb-form-fields').children().length === 0) {
                    $('.pfb-empty-form-message').show();
                }
            }
        });

        // Move field up
        $(document).on('click', '.pfb-field-move-up', function() {
            const $field = $(this).closest('.pfb-field');
            const $prev = $field.prev('.pfb-field');

            if ($prev.length) {
                $field.insertBefore($prev);
            }
        });

        // Move field down
        $(document).on('click', '.pfb-field-move-down', function() {
            const $field = $(this).closest('.pfb-field');
            const $next = $field.next('.pfb-field');

            if ($next.length) {
                $field.insertAfter($next);
            }
        });

        // Collapse/expand all fields
        $('#pfb-collapse-all-fields').on('click', function() {
            $('.pfb-field-settings.active').removeClass('active');
        });

        $('#pfb-expand-all-fields').on('click', function() {
            $('.pfb-field').each(function() {
                const fieldId = $(this).attr('id');
                $('#' + fieldId + ' .pfb-field-settings').addClass('active');
            });
        });

        // Conditional logic toggle
        $(document).on('change', '.pfb-enable-conditional-logic', function() {
            const $field = $(this).closest('.pfb-field');
            const $settings = $field.find('.pfb-conditional-logic-settings');

            if ($(this).is(':checked')) {
                $settings.slideDown(300);
                // Add first condition automatically
                addCondition($field);
                // Load available fields for conditions
                loadConditionalFields($field);
            } else {
                $settings.slideUp(300);
                // Clear conditions
                $field.find('.pfb-conditional-conditions').empty();
            }
        });

        // Add condition button
        $(document).on('click', '.pfb-add-condition', function() {
            const $field = $(this).closest('.pfb-field');
            addCondition($field);
            loadConditionalFields($field);
        });

        // Remove condition button
        $(document).on('click', '.pfb-remove-condition', function() {
            $(this).closest('.pfb-condition-row').remove();
        });

        // Save form with beautiful popup
        $(document).on('click', '#pfb-save-form', function(e) {
            e.preventDefault();
            console.log('PFB Admin: Save button clicked!');

            // Check if form has title
            const formTitle = $('#form_title').val().trim();
            if (!formTitle) {
                alert('Please enter a form title before saving.');
                $('#form_title').focus();
                return;
            }

            console.log('PFB Admin: Form title is valid, proceeding with save');

            // For now, let's use a simple confirmation and direct save
            if (confirm('Save form "' + formTitle + '"?')) {
                console.log('PFB Admin: User confirmed save, calling saveForm()');
                saveForm();
            } else {
                console.log('PFB Admin: User cancelled save');
            }
        });

        // Load form if editing
        const urlParams = new URLSearchParams(window.location.search);
        const formId = urlParams.get('form_id');

        if (formId) {
            loadForm(formId);
        }
    }

    /**
     * Create a new field element.
     */
    function createField(type, id) {
        const fieldTypes = pfb_data.field_types;
        const fieldLabel = fieldTypes[type] || type.charAt(0).toUpperCase() + type.slice(1);

        // Debug log for field creation
        console.log('Creating field of type:', type, 'with id:', id);

        let html = `
            <div id="${id}" class="pfb-field" data-type="${type}">
                <div class="pfb-field-header">
                    <div class="pfb-field-title">${fieldLabel}</div>
                    <div class="pfb-field-actions">
                        <div class="pfb-field-action pfb-field-move-up" title="Move Up"><span class="dashicons dashicons-arrow-up-alt2"></span></div>
                        <div class="pfb-field-action pfb-field-move-down" title="Move Down"><span class="dashicons dashicons-arrow-down-alt2"></span></div>
                        <div class="pfb-field-action pfb-field-edit" title="Edit"><span class="dashicons dashicons-edit"></span></div>
                        <div class="pfb-field-action pfb-field-duplicate" title="Duplicate"><span class="dashicons dashicons-admin-page"></span></div>
                        <div class="pfb-field-action pfb-field-delete" title="Delete"><span class="dashicons dashicons-trash"></span></div>
                    </div>
                </div>
                <div class="pfb-field-preview">
                    ${getFieldPreview(type)}
                </div>
                <div class="pfb-field-settings">
                    ${getFieldSettings(type, id)}
                </div>
            </div>
        `;

        const $field = $(html);

        // Debug log for field settings
        console.log('Field settings HTML:', $field.find('.pfb-field-settings').html());
        console.log('Width dropdown exists:', $field.find('.pfb-field-width-input').length > 0);

        return $field;
    }

    /**
     * Get field preview HTML.
     */
    function getFieldPreview(type) {
        switch (type) {
            case 'text':
                return '<input type="text" class="pfb-form-control" placeholder="Text Input" disabled>';

            case 'number':
                return '<input type="number" class="pfb-form-control" placeholder="0" disabled>';

            case 'slider':
                return `
                    <div class="pfb-slider-preview">
                        <input type="range" min="0" max="100" value="50" class="pfb-slider-control" disabled>
                        <div class="pfb-slider-value">50</div>
                    </div>
                `;

            case 'dropdown':
                return `
                    <select class="pfb-form-control" disabled>
                        <option>Option 1</option>
                        <option>Option 2</option>
                        <option>Option 3</option>
                    </select>
                `;

            case 'radio':
                return `
                    <div>
                        <label><input type="radio" name="radio_preview" disabled> Option 1</label><br>
                        <label><input type="radio" name="radio_preview" disabled> Option 2</label><br>
                        <label><input type="radio" name="radio_preview" disabled> Option 3</label>
                    </div>
                `;

            case 'checkbox':
                return `
                    <div>
                        <label><input type="checkbox" disabled> Option 1</label><br>
                        <label><input type="checkbox" disabled> Option 2</label><br>
                        <label><input type="checkbox" disabled> Option 3</label>
                    </div>
                `;

            case 'subtotal':
                return `
                    <div class="pfb-subtotal-preview">
                        <div class="pfb-subtotal-line">
                            <div class="pfb-subtotal-line-label">Line 1</div>
                            <div class="pfb-subtotal-line-value">$0.00</div>
                        </div>
                        <div class="pfb-subtotal-line">
                            <div class="pfb-subtotal-line-label">Line 2</div>
                            <div class="pfb-subtotal-line-value">$0.00</div>
                        </div>
                        <div class="pfb-subtotal-total">
                            <div class="pfb-subtotal-total-label">${pfb_data.i18n.subtotal || 'Subtotal'}</div>
                            <div class="pfb-subtotal-total-value">$0.00</div>
                        </div>
                    </div>
                `;

            case 'total':
                return '<div class="pfb-total-preview">$0.00</div>';

            default:
                return '<div>Field Preview</div>';
        }
    }

    /**
     * Get field settings HTML.
     */
    function getFieldSettings(type, id) {
        let html = `
            <div class="pfb-form-group">
                <label for="${id}_label">${pfb_data.i18n.field_label}</label>
                <input type="text" id="${id}_label" class="pfb-form-control pfb-field-label-input" value="${type.charAt(0).toUpperCase() + type.slice(1)}">
            </div>
            <div class="pfb-form-group">
                <label for="${id}_name">${pfb_data.i18n.field_name}</label>
                <input type="text" id="${id}_name" class="pfb-form-control pfb-field-name-input" value="${type}_${id.replace('field_', '')}">
                <div class="pfb-form-help">${pfb_data.i18n.field_identifier}</div>
            </div>
            <div class="pfb-form-group">
                <label>
                    <input type="checkbox" class="pfb-field-required-input"> ${pfb_data.i18n.required_field}
                </label>
            </div>
            <div class="pfb-form-group pfb-width-setting-group">
                <label for="${id}_width">${pfb_data.i18n.field_width || 'Field Width'}</label>
                <select id="${id}_width" class="pfb-form-control pfb-field-width-input">
                    <option value="100">${pfb_data.i18n.width_100 || '100% (Full Width)'}</option>
                    <option value="50">${pfb_data.i18n.width_50 || '50% (Half Width)'}</option>
                    <option value="33">${pfb_data.i18n.width_33 || '33% (One Third)'}</option>
                    <option value="25">${pfb_data.i18n.width_25 || '25% (Quarter Width)'}</option>
                </select>
                <div class="pfb-form-help">${pfb_data.i18n.field_width_help || 'Select the width of this field to place multiple fields in the same row'}</div>
            </div>
            ${type === 'text' ? `
            <div class="pfb-form-group">
                <label>
                    <input type="checkbox" class="pfb-field-hidden-input"> Make this field hidden
                </label>
                <div class="pfb-form-help">Hidden fields will not be visible to users but their values will be used in calculations</div>
            </div>
            <div class="pfb-form-group pfb-hidden-field-options" style="display: none;">
                <label for="${id}_variable">Price Variable</label>
                <div class="pfb-option-value-container">
                    <input type="text" id="${id}_variable_value" class="pfb-form-control pfb-hidden-field-value" placeholder="Variable or value" value="">
                    <button type="button" class="pfb-dynamic-value-button pfb-select-hidden-variable">
                        <span class="dashicons dashicons-database"></span>
                    </button>
                    <input type="hidden" id="${id}_variable" class="pfb-hidden-field-variable" value="">
                </div>
                <div class="pfb-form-help">Select a price variable or enter a default value for this hidden field</div>
            </div>
            ` : ''}
        `;

        // Add type-specific settings
        switch (type) {
            case 'slider':
                html += `
                    <div class="pfb-form-group">
                        <label for="${id}_min">Minimum Value</label>
                        <input type="number" id="${id}_min" class="pfb-form-control pfb-slider-min-input" value="0">
                    </div>
                    <div class="pfb-form-group">
                        <label for="${id}_max">Maximum Value</label>
                        <input type="number" id="${id}_max" class="pfb-form-control pfb-slider-max-input" value="100">
                    </div>
                    <div class="pfb-form-group">
                        <label for="${id}_step">Step</label>
                        <input type="number" id="${id}_step" class="pfb-form-control pfb-slider-step-input" value="1">
                        <div class="pfb-form-help">The step value determines the size of each increment. For example, a step of 2 will create values like 0, 2, 4, 6...</div>
                    </div>
                    <div class="pfb-form-group">
                        <label for="${id}_default">Default Value</label>
                        <input type="number" id="${id}_default" class="pfb-form-control pfb-slider-default-input" value="50">
                    </div>
                `;
                break;

            case 'subtotal':
                html += `
                    <div class="pfb-form-group">
                        <label>${pfb_data.i18n.subtotal_lines || 'Subtotal Lines'}</label>
                        <div class="pfb-subtotal-lines-header">
                            <div class="pfb-subtotal-line-label-header">${pfb_data.i18n.line_label || 'Line Label'}</div>
                            <div class="pfb-subtotal-line-formula-header">${pfb_data.i18n.formula || 'Formula'}</div>
                            <div class="pfb-subtotal-line-action-header"></div>
                        </div>
                        <div class="pfb-subtotal-lines">
                            <div class="pfb-subtotal-line">
                                <input type="text" class="pfb-form-control pfb-subtotal-line-label" placeholder="${pfb_data.i18n.line_label || 'Line Label'}" value="${pfb_data.i18n.line || 'Line'} 1">
                                <div class="pfb-formula-input-container">
                                    <textarea class="pfb-form-control pfb-subtotal-line-formula" rows="2" placeholder="${pfb_data.i18n.formula || 'Formula'}"></textarea>
                                    <button type="button" class="pfb-select-subtotal-formula">
                                        <span class="dashicons dashicons-editor-code"></span>
                                    </button>
                                </div>
                                <button type="button" class="pfb-btn pfb-add-subtotal-line">+</button>
                            </div>
                        </div>
                        <div class="pfb-form-help">${pfb_data.i18n.subtotal_lines_help || 'Add multiple lines with labels and formulas. The subtotal will be the sum of all lines.'}</div>
                    </div>

                    <div class="pfb-formula-tools" style="display: none;">
                        <div class="pfb-formula-section">
                            <div class="pfb-formula-section-title">${pfb_data.i18n.fields || 'Fields'}</div>
                            <div class="pfb-formula-fields"></div>
                        </div>
                        <div class="pfb-formula-section">
                            <div class="pfb-formula-section-title">${pfb_data.i18n.variables || 'Variables'}</div>
                            <div class="pfb-formula-variables"></div>
                        </div>
                        <div class="pfb-formula-section">
                            <div class="pfb-formula-section-title">${pfb_data.i18n.operators || 'Operators'}</div>
                            <div class="pfb-formula-operators">
                                <button type="button" class="pfb-formula-operator" data-operator="+">${pfb_data.i18n.addition || '+'}</button>
                                <button type="button" class="pfb-formula-operator" data-operator="-">${pfb_data.i18n.subtraction || '-'}</button>
                                <button type="button" class="pfb-formula-operator" data-operator="*">${pfb_data.i18n.multiplication || '*'}</button>
                                <button type="button" class="pfb-formula-operator" data-operator="/">${pfb_data.i18n.division || '/'}</button>
                                <button type="button" class="pfb-formula-operator" data-operator="(">${pfb_data.i18n.parentheses || '('}</button>
                                <button type="button" class="pfb-formula-operator" data-operator=")">${pfb_data.i18n.parentheses || ')'}</button>
                            </div>
                        </div>
                        <div class="pfb-formula-section">
                            <div class="pfb-formula-section-title">${pfb_data.i18n.functions || 'Functions'}</div>
                            <div class="pfb-formula-functions">
                                <button type="button" class="pfb-formula-function" data-function="ceil()">${pfb_data.i18n.ceil || 'Ceil'}</button>
                                <button type="button" class="pfb-formula-function" data-function="floor()">${pfb_data.i18n.floor || 'Floor'}</button>
                                <button type="button" class="pfb-formula-function" data-function="round()">${pfb_data.i18n.round || 'Round'}</button>
                                <button type="button" class="pfb-formula-function" data-function="min()">${pfb_data.i18n.min || 'Min'}</button>
                                <button type="button" class="pfb-formula-function" data-function="max()">${pfb_data.i18n.max || 'Max'}</button>
                            </div>
                        </div>
                        <div class="pfb-formula-section">
                            <div class="pfb-formula-section-title">${pfb_data.i18n.numbers || 'Numbers'}</div>
                            <div class="pfb-formula-numbers">
                                <button type="button" class="pfb-formula-number" data-number="1">1</button>
                                <button type="button" class="pfb-formula-number" data-number="2">2</button>
                                <button type="button" class="pfb-formula-number" data-number="3">3</button>
                                <button type="button" class="pfb-formula-number" data-number="4">4</button>
                                <button type="button" class="pfb-formula-number" data-number="5">5</button>
                                <button type="button" class="pfb-formula-number" data-number="6">6</button>
                                <button type="button" class="pfb-formula-number" data-number="7">7</button>
                                <button type="button" class="pfb-formula-number" data-number="8">8</button>
                                <button type="button" class="pfb-formula-number" data-number="9">9</button>
                                <button type="button" class="pfb-formula-number" data-number="0">0</button>
                                <button type="button" class="pfb-formula-number" data-number=".">.</button>
                                <button type="button" class="pfb-clear-formula">${pfb_data.i18n.clear || 'Clear'}</button>
                            </div>
                        </div>
                    </div>

                    <div class="pfb-form-group">
                        <label for="${id}_empty_value">${pfb_data.i18n.empty_value_display || 'Empty Value Display'}</label>
                        <input type="text" id="${id}_empty_value" class="pfb-subtotal-empty-value-input" value="---">
                        <div class="pfb-form-help">${pfb_data.i18n.empty_value_help || 'Text to display when a line cannot be calculated (e.g., when required fields are not filled).'}</div>
                    </div>
                `;
                break;

            case 'dropdown':
            case 'radio':
            case 'checkbox':
                html += `
                    <div class="pfb-form-group">
                        <label>${pfb_data.i18n.options}</label>
                        <div class="pfb-field-options-header">
                            <div class="pfb-field-option-label-header">${pfb_data.i18n.label}</div>
                            <div class="pfb-field-option-value-header">${pfb_data.i18n.value}</div>
                            <div class="pfb-field-option-action-header"></div>
                        </div>
                        <div class="pfb-field-options">
                            <div class="pfb-field-option">
                                <input type="text" class="pfb-form-control pfb-option-label" placeholder="${pfb_data.i18n.label}" value="${pfb_data.i18n.option} 1">
                                <div class="pfb-option-value-container">
                                    <input type="text" class="pfb-form-control pfb-option-value" placeholder="${pfb_data.i18n.value}" value="10">
                                    <button type="button" class="pfb-dynamic-value-button pfb-select-dynamic-value">
                                        <span class="dashicons dashicons-database"></span>
                                    </button>
                                    <input type="hidden" class="pfb-option-variable" value="">
                                </div>
                                <button type="button" class="pfb-btn pfb-btn-secondary pfb-add-option">+</button>
                            </div>
                            <div class="pfb-field-option">
                                <input type="text" class="pfb-form-control pfb-option-label" placeholder="${pfb_data.i18n.label}" value="${pfb_data.i18n.option} 2">
                                <div class="pfb-option-value-container">
                                    <input type="text" class="pfb-form-control pfb-option-value" placeholder="${pfb_data.i18n.value}" value="20">
                                    <button type="button" class="pfb-dynamic-value-button pfb-select-dynamic-value">
                                        <span class="dashicons dashicons-database"></span>
                                    </button>
                                    <input type="hidden" class="pfb-option-variable" value="">
                                </div>
                                <button type="button" class="pfb-btn pfb-btn-secondary pfb-remove-option">-</button>
                            </div>
                            <div class="pfb-field-option">
                                <input type="text" class="pfb-form-control pfb-option-label" placeholder="${pfb_data.i18n.label}" value="${pfb_data.i18n.option} 3">
                                <div class="pfb-option-value-container">
                                    <input type="text" class="pfb-form-control pfb-option-value" placeholder="${pfb_data.i18n.value}" value="30">
                                    <button type="button" class="pfb-dynamic-value-button pfb-select-dynamic-value">
                                        <span class="dashicons dashicons-database"></span>
                                    </button>
                                    <input type="hidden" class="pfb-option-variable" value="">
                                </div>
                                <button type="button" class="pfb-btn pfb-btn-secondary pfb-remove-option">-</button>
                            </div>
                        </div>
                        <div class="pfb-form-help">${pfb_data.i18n.option_help}</div>
                    </div>
                `;
                break;

            case 'total':
                html += `
                    <div class="pfb-form-group">
                        <label for="${id}_formula">${pfb_data.i18n.price_formula}</label>
                        <div class="pfb-formula-builder">
                            <div class="pfb-formula-input-container">
                                <textarea id="${id}_formula" class="pfb-form-control pfb-field-formula-input" rows="4"></textarea>
                                <button type="button" class="pfb-btn pfb-btn-secondary pfb-clear-formula">${pfb_data.i18n.clear}</button>
                            </div>
                            <div class="pfb-formula-tools">
                                <div class="pfb-formula-section">
                                    <div class="pfb-formula-section-title">${pfb_data.i18n.form_fields}</div>
                                    <div class="pfb-formula-fields">
                                        <!-- Fields will be loaded dynamically -->
                                        <div class="pfb-formula-empty">${pfb_data.i18n.no_fields}</div>
                                    </div>
                                </div>
                                <div class="pfb-formula-section">
                                    <div class="pfb-formula-section-title">${pfb_data.i18n.price_variables}</div>
                                    <button type="button" class="pfb-btn pfb-btn-secondary pfb-select-formula-variables">
                                        <span class="dashicons dashicons-database"></span> ${pfb_data.i18n.select_variables}
                                    </button>
                                </div>
                                <div class="pfb-formula-section">
                                    <div class="pfb-formula-section-title">${pfb_data.i18n.operators}</div>
                                    <div class="pfb-formula-operators">
                                        <button type="button" class="pfb-formula-operator" data-operator="+">+</button>
                                        <button type="button" class="pfb-formula-operator" data-operator="-">-</button>
                                        <button type="button" class="pfb-formula-operator" data-operator="*">×</button>
                                        <button type="button" class="pfb-formula-operator" data-operator="/">÷</button>
                                        <button type="button" class="pfb-formula-operator" data-operator="(">(</button>
                                        <button type="button" class="pfb-formula-operator" data-operator=")">)</button>
                                    </div>
                                </div>
                                <div class="pfb-formula-section">
                                    <div class="pfb-formula-section-title">${pfb_data.i18n.numbers}</div>
                                    <div class="pfb-formula-numbers">
                                        <button type="button" class="pfb-formula-number" data-number="0">0</button>
                                        <button type="button" class="pfb-formula-number" data-number="1">1</button>
                                        <button type="button" class="pfb-formula-number" data-number="2">2</button>
                                        <button type="button" class="pfb-formula-number" data-number="3">3</button>
                                        <button type="button" class="pfb-formula-number" data-number="4">4</button>
                                        <button type="button" class="pfb-formula-number" data-number="5">5</button>
                                        <button type="button" class="pfb-formula-number" data-number="6">6</button>
                                        <button type="button" class="pfb-formula-number" data-number="7">7</button>
                                        <button type="button" class="pfb-formula-number" data-number="8">8</button>
                                        <button type="button" class="pfb-formula-number" data-number="9">9</button>
                                        <button type="button" class="pfb-formula-number" data-number=".">.</button>
                                    </div>
                                </div>
                                <div class="pfb-formula-section">
                                    <div class="pfb-formula-section-title">${pfb_data.i18n.functions}</div>
                                    <div class="pfb-formula-functions">
                                        <button type="button" class="pfb-formula-function" data-function="round(">${pfb_data.i18n.round}</button>
                                        <button type="button" class="pfb-formula-function" data-function="ceil(">${pfb_data.i18n.ceil}</button>
                                        <button type="button" class="pfb-formula-function" data-function="floor(">${pfb_data.i18n.floor}</button>
                                        <button type="button" class="pfb-formula-function" data-function="min(">${pfb_data.i18n.min}</button>
                                        <button type="button" class="pfb-formula-function" data-function="max(">${pfb_data.i18n.max}</button>
                                        <button type="button" class="pfb-formula-function" data-function="if(">${pfb_data.i18n.if}</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="pfb-form-help">
                            ${pfb_data.i18n.formula_help}<br>
                            ${pfb_data.i18n.ceil_help}
                        </div>
                    </div>
                `;
                break;
        }

        // Add conditional logic section for all field types except total
        if (type !== 'total') {
            html += `
                <div class="pfb-form-group pfb-conditional-section">
                    <h4 class="pfb-section-title">
                        <span class="dashicons dashicons-randomize"></span>
                        Conditional Logic
                    </h4>
                    <label class="pfb-toggle-switch">
                        <input type="checkbox" class="pfb-enable-conditional-logic">
                        <span class="pfb-toggle-slider"></span>
                        <span class="pfb-toggle-text">Enable conditional logic for this field</span>
                    </label>
                    <div class="pfb-form-help">Show or hide this field based on other field values</div>
                </div>

                <div class="pfb-conditional-logic-settings" style="display: none;">
                    <div class="pfb-conditional-rule-builder">
                        <div class="pfb-rule-sentence">
                            <select class="pfb-form-control pfb-conditional-action">
                                <option value="show">Show</option>
                                <option value="hide">Hide</option>
                            </select>
                            <span class="pfb-rule-text">this field when</span>
                            <select class="pfb-form-control pfb-conditional-operator">
                                <option value="and">ALL</option>
                                <option value="or">ANY</option>
                            </select>
                            <span class="pfb-rule-text">of the following conditions are met:</span>
                        </div>

                        <div class="pfb-conditions-container">
                            <div class="pfb-conditional-conditions">
                                <!-- Conditions will be added here -->
                            </div>
                            <button type="button" class="pfb-btn pfb-btn-outline pfb-add-condition">
                                <span class="dashicons dashicons-plus-alt"></span>
                                Add Condition
                            </button>
                        </div>
                    </div>
                </div>
            `;
        }

        html += `
            <div class="pfb-form-group">
                <button type="button" class="pfb-btn pfb-btn-primary pfb-save-field-settings">${pfb_data.i18n.save_settings}</button>
                <button type="button" class="pfb-btn pfb-btn-secondary pfb-cancel-field-settings">${pfb_data.i18n.cancel}</button>
            </div>
        `;

        return html;
    }

    /**
     * Toggle field settings panel.
     */
    function toggleFieldSettings(fieldId) {
        const $field = $('#' + fieldId);
        const $settings = $field.find('.pfb-field-settings');

        if ($settings.hasClass('active')) {
            $settings.removeClass('active');
        } else {
            // Close other open settings
            $('.pfb-field-settings.active').removeClass('active');

            // Open this field's settings
            $settings.addClass('active');

            // Debug log for field settings
            console.log('Field settings opened for field:', fieldId);
            console.log('Field settings HTML:', $settings.html());

            // Check if width dropdown exists
            const $widthDropdown = $field.find('.pfb-field-width-input');
            console.log('Width dropdown exists:', $widthDropdown.length > 0);

            // If width dropdown doesn't exist, add it
            if ($widthDropdown.length === 0) {
                console.log('Width dropdown not found, adding it');
                const $widthGroup = $(`
                    <div class="pfb-form-group pfb-width-setting-group">
                        <label for="${fieldId}_width">Field Width</label>
                        <select id="${fieldId}_width" class="pfb-form-control pfb-field-width-input">
                            <option value="100">100% (Full Width)</option>
                            <option value="50">50% (Half Width)</option>
                            <option value="33">33% (One Third)</option>
                            <option value="25">25% (Quarter Width)</option>
                        </select>
                        <div class="pfb-form-help">Select the width of this field to place multiple fields in the same row</div>
                    </div>
                `);

                // Insert after the required field checkbox
                $field.find('.pfb-field-required-input').closest('.pfb-form-group').after($widthGroup);
            }

            // Load price variables if needed
            const fieldType = $field.data('type');
            if (['dropdown', 'radio', 'checkbox'].includes(fieldType)) {
                // Load variables for each option
                $field.find('.pfb-option-variable').each(function() {
                    loadPriceVariables($(this));
                });
            }

            // Load fields for formula builder
            if (fieldType === 'total' || fieldType === 'subtotal') {
                // First, remove any existing event handlers to prevent duplicates
                $field.off('click', '.pfb-formula-field');
                $field.off('click', '.pfb-formula-variable');
                $field.off('click', '.pfb-formula-number');
                $field.off('click', '.pfb-formula-operator');
                $field.off('click', '.pfb-formula-function');
                $field.off('click', '.pfb-clear-formula');
                $field.off('click', '.pfb-select-formula-variables');

                // Load fields for the formula builder
                loadFormulaFields($field);

                // Add event handlers for formula builder elements
                initFormulaBuilder($field);

                // For subtotal fields, hide the formula tools initially
                if (fieldType === 'subtotal') {
                    $field.find('.pfb-formula-tools').hide();
                }
            }
        }
    }

    /**
     * Load price variables into a select element.
     */
    function loadPriceVariables($select) {
        $.ajax({
            url: pfb_data.ajax_url,
            type: 'GET',
            data: {
                action: 'pfb_get_price_variables',
                nonce: pfb_data.nonce
            },
            success: function(response) {
                if (response.success && response.data.variables) {
                    const variables = response.data.variables;
                    const categories = response.data.categories;

                    // Clear existing options except the first one
                    $select.find('option:not(:first)').remove();

                    // Group variables by category
                    const groupedVariables = {};

                    categories.forEach(function(category) {
                        groupedVariables[category.id] = {
                            name: category.name,
                            variables: []
                        };
                    });

                    variables.forEach(function(variable) {
                        if (groupedVariables[variable.category_id]) {
                            groupedVariables[variable.category_id].variables.push(variable);
                        }
                    });

                    // Add variables to select
                    for (const categoryId in groupedVariables) {
                        const category = groupedVariables[categoryId];

                        if (category.variables.length > 0) {
                            const $optgroup = $('<optgroup label="' + category.name + '">');

                            category.variables.forEach(function(variable) {
                                $optgroup.append('<option value="' + variable.variable_key + '">' + variable.name + '</option>');
                            });

                            $select.append($optgroup);
                        }
                    }
                }
            }
        });
    }

    /**
     * Load form fields for the formula builder.
     */
    function loadFormulaFields($field) {
        const $formulaFields = $field.find('.pfb-formula-fields');

        // Clear existing fields
        $formulaFields.empty();

        // Get all form fields except the current one
        const fields = [];
        $('.pfb-form-fields .pfb-field').each(function() {
            const fieldId = $(this).attr('id');

            // Skip the current field (total field)
            if (fieldId === $field.attr('id')) {
                return;
            }

            const fieldName = $(this).find('.pfb-field-name-input').val();
            const fieldLabel = $(this).find('.pfb-field-label-input').val();

            if (fieldName) {
                fields.push({
                    id: fieldId,
                    name: fieldName,
                    label: fieldLabel || fieldName
                });
            }
        });

        // Add fields to the formula builder
        if (fields.length > 0) {
            fields.forEach(function(field) {
                const $button = $('<button type="button" class="pfb-formula-field" data-field="' + field.name + '">' + field.label + '</button>');
                $formulaFields.append($button);
            });
        } else {
            $formulaFields.html('<div class="pfb-formula-empty">No fields available yet</div>');
        }

        // Also load price variables
        loadFormulaVariables($field);
    }

    /**
     * Add a field to the form.
     */
    function addField(fieldType) {
        console.log('PFB Admin: Adding field of type:', fieldType);

        const fieldId = 'field_' + Date.now();

        // Create new field element using the existing createField function
        const $field = createField(fieldType, fieldId);

        // Add to form
        $('.pfb-form-fields').append($field);

        // Hide empty message
        $('.pfb-empty-form-message').hide();

        // Open field settings automatically
        setTimeout(function() {
            $field.find('.pfb-field-edit').trigger('click');
        }, 100);

        // Scroll to new field
        $field[0].scrollIntoView({ behavior: 'smooth' });

        console.log('PFB Admin: Field added successfully');
    }

    /**
     * Load available fields for conditional logic.
     */
    function loadConditionalFields($field) {
        const currentFieldId = $field.attr('id');

        // Get all condition field selects in this field
        $field.find('.pfb-condition-field').each(function() {
            const $select = $(this);

            // Clear existing options except the first one
            $select.find('option:not(:first)').remove();

            // Get all form fields except the current one
            $('.pfb-form-fields .pfb-field').each(function() {
                const fieldId = $(this).attr('id');

                // Skip the current field
                if (fieldId === currentFieldId) {
                    return;
                }

                const fieldName = $(this).find('.pfb-field-name-input').val();
                const fieldLabel = $(this).find('.pfb-field-label-input').val();
                const fieldType = $(this).data('type');

                if (fieldName && fieldLabel) {
                    $select.append(`<option value="${fieldName}" data-type="${fieldType}">${fieldLabel} (${fieldName})</option>`);
                }
            });
        });
    }

    /**
     * Load price variables for the formula builder.
     */
    function loadFormulaVariables($field) {
        const $formulaVariables = $field.find('.pfb-formula-variables');

        // Show loading message
        $formulaVariables.html('<div class="pfb-formula-empty">Loading variables...</div>');

        // Load variables via AJAX
        $.ajax({
            url: pfb_data.ajax_url,
            type: 'GET',
            data: {
                action: 'pfb_get_price_variables',
                nonce: pfb_data.nonce
            },
            success: function(response) {
                if (response.success && response.data.variables) {
                    $formulaVariables.empty();

                    let hasVariables = false;

                    // Group variables by category
                    const categories = {};

                    response.data.variables.forEach(function(variable) {
                        if (!categories[variable.category_id]) {
                            categories[variable.category_id] = {
                                name: variable.category_name,
                                variables: []
                            };
                        }

                        categories[variable.category_id].variables.push(variable);
                        hasVariables = true;
                    });

                    // Add variables to the formula builder
                    if (hasVariables) {
                        Object.values(categories).forEach(function(category) {
                            category.variables.forEach(function(variable) {
                                const $button = $('<button type="button" class="pfb-formula-variable" data-variable="' + variable.variable_key + '">' + variable.name + '</button>');
                                $formulaVariables.append($button);
                            });
                        });
                    } else {
                        $formulaVariables.html('<div class="pfb-formula-empty">No variables available</div>');
                    }
                } else {
                    $formulaVariables.html('<div class="pfb-formula-empty">Failed to load variables</div>');
                }
            },
            error: function() {
                $formulaVariables.html('<div class="pfb-formula-empty">Failed to load variables</div>');
            }
        });
    }

    /**
     * Load dynamic values for the modal.
     */
    function loadDynamicValues() {
        const $categories = $('.pfb-dynamic-value-categories');

        // Show loading message
        $categories.html('<div class="pfb-dynamic-value-loading">Loading variables...</div>');

        // Load variables via AJAX
        $.ajax({
            url: pfb_data.ajax_url,
            type: 'GET',
            data: {
                action: 'pfb_get_price_variables',
                nonce: pfb_data.nonce
            },
            success: function(response) {
                if (response.success && response.data.variables && response.data.categories) {
                    const variables = response.data.variables;
                    const categories = response.data.categories;

                    // Clear loading message
                    $categories.empty();

                    // Group variables by category
                    const groupedVariables = {};

                    categories.forEach(function(category) {
                        groupedVariables[category.id] = {
                            name: category.name,
                            variables: []
                        };
                    });

                    variables.forEach(function(variable) {
                        if (groupedVariables[variable.category_id]) {
                            groupedVariables[variable.category_id].variables.push(variable);
                        }
                    });

                    // Add categories and variables to the modal
                    let hasVariables = false;

                    for (const categoryId in groupedVariables) {
                        const category = groupedVariables[categoryId];

                        if (category.variables.length > 0) {
                            hasVariables = true;

                            const $category = $('<div class="pfb-dynamic-value-category">');
                            $category.append('<div class="pfb-dynamic-value-category-header">' + category.name + '</div>');

                            const $list = $('<div class="pfb-dynamic-value-list">');

                            category.variables.forEach(function(variable) {
                                const $item = $('<div class="pfb-dynamic-value-item" data-variable="' + variable.variable_key + '">');
                                $item.append('<div class="pfb-dynamic-value-name">' + variable.name + '</div>');
                                $item.append('<div class="pfb-dynamic-value-key">{' + variable.variable_key + '}</div>');
                                $list.append($item);
                            });

                            $category.append($list);
                            $categories.append($category);
                        }
                    }

                    if (!hasVariables) {
                        $categories.html('<div class="pfb-dynamic-value-empty">No variables available. Please add variables in the Price Variables section.</div>');
                    }

                    // Add click handler for variable items
                    $('.pfb-dynamic-value-item').on('click', function() {
                        const variable = $(this).data('variable');
                        const $valueInput = $('#pfb-dynamic-value-modal').data('valueInput');
                        const $variableInput = $('#pfb-dynamic-value-modal').data('variableInput');

                        // Set the value input to the variable placeholder
                        if ($valueInput) {
                            $valueInput.val('{' + variable + '}');
                            console.log('Set value input to:', '{' + variable + '}');
                        } else {
                            console.error('Value input not found');
                        }

                        // Store the variable key in the hidden input
                        if ($variableInput) {
                            $variableInput.val(variable);
                            console.log('Set variable input to:', variable);
                        } else {
                            console.error('Variable input not found');
                        }

                        // Log the current state of the inputs
                        console.log('Current state after selection:', {
                            valueInput: $valueInput ? $valueInput.val() : 'not found',
                            variableInput: $variableInput ? $variableInput.val() : 'not found'
                        });

                        // Close the modal
                        $('#pfb-dynamic-value-modal').hide();
                    });

                    // Add search functionality
                    $('#pfb-dynamic-value-search-input').on('input', function() {
                        const searchTerm = $(this).val().toLowerCase();

                        $('.pfb-dynamic-value-item').each(function() {
                            const name = $(this).find('.pfb-dynamic-value-name').text().toLowerCase();
                            const key = $(this).find('.pfb-dynamic-value-key').text().toLowerCase();

                            if (name.includes(searchTerm) || key.includes(searchTerm)) {
                                $(this).show();
                            } else {
                                $(this).hide();
                            }
                        });

                        // Show/hide categories based on visible items
                        $('.pfb-dynamic-value-category').each(function() {
                            const $category = $(this);
                            const hasVisibleItems = $category.find('.pfb-dynamic-value-item:visible').length > 0;

                            if (hasVisibleItems) {
                                $category.show();
                            } else {
                                $category.hide();
                            }
                        });
                    });
                } else {
                    $categories.html('<div class="pfb-dynamic-value-empty">Failed to load variables. Please try again.</div>');
                }
            },
            error: function() {
                $categories.html('<div class="pfb-dynamic-value-empty">Failed to load variables. Please try again.</div>');
            }
        });
    }

    // Close dynamic value modal
    $(document).on('click', '.pfb-modal-close, .pfb-modal-cancel', function() {
        $('.pfb-modal').hide();
    });

    // Prevent modal close when clicking inside
    $(document).on('click', '.pfb-modal-content', function(e) {
        e.stopPropagation();
    });

    // Close modal when clicking outside
    $(document).on('click', '.pfb-modal', function() {
        $(this).hide();
    });

    // Handle formula variable item click
    $(document).on('click', '.pfb-formula-variable-item', function() {
        const variableName = $(this).data('variable');
        const $formulaInput = $('#pfb-formula-variables-modal').data('formulaInput');

        if ($formulaInput) {
            insertAtCursor($formulaInput[0], '{' + variableName + '}');

            // Close the modal
            $('#pfb-formula-variables-modal').hide();
        }
    });

    /**
     * Initialize formula builder event handlers.
     */
    function initFormulaBuilder($field) {
        const $formulaInput = $field.find('.pfb-field-formula-input');
        const fieldType = $field.data('type');

        // Function to get the current formula input
        function getCurrentFormulaInput() {
            // For subtotal fields, use the stored reference to the current formula input
            if (fieldType === 'subtotal') {
                return $field.data('currentFormulaInput') || $formulaInput;
            }
            // For total fields, use the main formula input
            return $formulaInput;
        }

        // Field button click
        $field.on('click', '.pfb-formula-field', function() {
            const fieldName = $(this).data('field');
            const $currentInput = getCurrentFormulaInput();
            insertAtCursor($currentInput[0], '{' + fieldName + '}');
        });

        // Variable button click
        $field.on('click', '.pfb-formula-variable', function() {
            const variableName = $(this).data('variable');
            const $currentInput = getCurrentFormulaInput();
            insertAtCursor($currentInput[0], '{' + variableName + '}');
        });

        // Number button click
        $field.on('click', '.pfb-formula-number', function() {
            const number = $(this).data('number');
            const $currentInput = getCurrentFormulaInput();
            insertAtCursor($currentInput[0], number);
        });

        // Operator button click
        $field.on('click', '.pfb-formula-operator', function() {
            const operator = $(this).data('operator');
            const $currentInput = getCurrentFormulaInput();

            // Special handling for parentheses
            if (operator === '(') {
                // Always insert a pair of parentheses and position cursor between them
                insertAtCursor($currentInput[0], '()');

                // Move cursor between the parentheses
                const cursorPos = $currentInput[0].selectionStart;
                $currentInput[0].selectionStart = cursorPos - 1;
                $currentInput[0].selectionEnd = cursorPos - 1;
                $currentInput[0].focus();

                // Show a helpful message
                showFormulaInfo($field, 'Parentheses added. Type your expression between them.');
            } else if (operator === ')') {
                // We don't need this button anymore since we're always inserting paired parentheses
                showFormulaInfo($field, 'Use the ( button to add a pair of parentheses.');
            } else {
                // For all other operators, just insert them
                insertAtCursor($currentInput[0], operator);
            }
        });

        // Function button click
        $field.on('click', '.pfb-formula-function', function() {
            const func = $(this).data('function');
            const funcName = func.substring(0, func.length - 1);
            const $currentInput = getCurrentFormulaInput();

            // Always insert the function with empty parentheses
            insertAtCursor($currentInput[0], funcName + '()');

            // Position cursor inside the parentheses
            const cursorPos = $currentInput[0].selectionStart;
            $currentInput[0].selectionStart = cursorPos - 1;
            $currentInput[0].selectionEnd = cursorPos - 1;
            $currentInput[0].focus();

            // Show a helpful tooltip about the function
            let helpText = '';
            switch (funcName) {
                case 'ceil':
                    helpText = 'Rounds a number up to the next whole number. Example: ceil(4.3) = 5';
                    break;
                case 'floor':
                    helpText = 'Rounds a number down to the previous whole number. Example: floor(4.7) = 4';
                    break;
                case 'round':
                    helpText = 'Rounds a number to the nearest whole number. Example: round(4.5) = 5';
                    break;
                case 'min':
                    helpText = 'Returns the smallest of the given numbers. Example: min(4, 7, 2) = 2';
                    break;
                case 'max':
                    helpText = 'Returns the largest of the given numbers. Example: max(4, 7, 2) = 7';
                    break;
            }

            if (helpText) {
                showFormulaInfo($field, helpText);
            }
        });

        // Clear button click
        $field.on('click', '.pfb-clear-formula', function() {
            const $currentInput = getCurrentFormulaInput();
            $currentInput.val('');
            $currentInput.focus();
        });

        // Open variables modal
        $field.on('click', '.pfb-select-formula-variables', function() {
            const $currentInput = getCurrentFormulaInput();

            // Store reference to the formula input
            $('#pfb-formula-variables-modal').data('formulaInput', $currentInput);

            // Load variables
            loadFormulaVariablesForModal();

            // Show modal
            $('#pfb-formula-variables-modal').show();
        });
    }

    /**
     * Load price variables for the formula variables modal.
     */
    function loadFormulaVariablesForModal() {
        const $categories = $('#pfb-formula-variables-modal .pfb-formula-variables-list');

        // Show loading message
        $categories.html('<div class="pfb-formula-empty">Loading variables...</div>');

        // Load variables via AJAX
        $.ajax({
            url: pfb_data.ajax_url,
            type: 'GET',
            data: {
                action: 'pfb_get_price_variables',
                nonce: pfb_data.nonce
            },
            success: function(response) {
                if (response.success && response.data.variables) {
                    $categories.empty();

                    let hasVariables = false;

                    // Group variables by category
                    const categories = {};

                    response.data.variables.forEach(function(variable) {
                        if (!categories[variable.category_id]) {
                            categories[variable.category_id] = {
                                name: variable.category_name,
                                variables: []
                            };
                        }

                        categories[variable.category_id].variables.push(variable);
                        hasVariables = true;
                    });

                    // Add variables to the modal
                    if (hasVariables) {
                        Object.values(categories).forEach(function(category) {
                            const $category = $('<div class="pfb-formula-variables-category"></div>');
                            $category.append('<h4>' + category.name + '</h4>');

                            const $variables = $('<div class="pfb-formula-variables-items"></div>');

                            category.variables.forEach(function(variable) {
                                const $variable = $('<button type="button" class="pfb-formula-variable-item" data-variable="' + variable.variable_key + '">' + variable.name + '</button>');
                                $variables.append($variable);
                            });

                            $category.append($variables);
                            $categories.append($category);
                        });
                    } else {
                        $categories.html('<div class="pfb-formula-empty">No variables available</div>');
                    }
                } else {
                    $categories.html('<div class="pfb-formula-empty">Failed to load variables</div>');
                }
            },
            error: function() {
                $categories.html('<div class="pfb-formula-empty">Failed to load variables</div>');
            }
        });
    }

    /**
     * Debug function to log form data before saving
     */
    function logFormData(formData) {
        console.log('Form data to be saved:', JSON.parse(JSON.stringify(formData)));

        // Log field options specifically
        if (formData.fields && formData.fields.length > 0) {
            formData.fields.forEach(function(field, index) {
                if (field.options && field.options.items) {
                    console.log(`Field ${index} (${field.type}) options:`, JSON.parse(JSON.stringify(field.options.items)));
                }
            });
        }
    }

    /**
     * Insert text at cursor position in a textarea.
     */
    function insertAtCursor(textarea, text) {
        if (textarea.selectionStart || textarea.selectionStart === 0) {
            const startPos = textarea.selectionStart;
            const endPos = textarea.selectionEnd;

            textarea.value = textarea.value.substring(0, startPos) + text + textarea.value.substring(endPos, textarea.value.length);

            // Set cursor position after inserted text
            textarea.selectionStart = startPos + text.length;
            textarea.selectionEnd = startPos + text.length;
        } else {
            textarea.value += text;
        }

        // Focus the textarea
        textarea.focus();
    }

    /**
     * Show beautiful save popup with validation.
     */
    function showSavePopup() {
        console.log('PFB Admin: showSavePopup() called');

        // Remove any existing popup first
        $('.pfb-save-popup-overlay').remove();

        try {
            // Create beautiful popup HTML
            const popupHtml = `
                <div class="pfb-save-popup-overlay">
                    <div class="pfb-save-popup">
                        <div class="pfb-popup-header">
                            <h3><span class="dashicons dashicons-saved"></span> Save Form</h3>
                            <button class="pfb-popup-close">&times;</button>
                        </div>
                        <div class="pfb-popup-content">
                            <div class="pfb-save-validation">
                                <div class="pfb-validation-item">
                                    <span class="pfb-validation-icon dashicons dashicons-yes-alt"></span>
                                    <span class="pfb-validation-text">Form title: <strong id="pfb-popup-title"></strong></span>
                                </div>
                                <div class="pfb-validation-item">
                                    <span class="pfb-validation-icon dashicons dashicons-yes-alt"></span>
                                    <span class="pfb-validation-text">Fields: <strong id="pfb-popup-fields-count"></strong></span>
                                </div>
                                <div class="pfb-validation-item">
                                    <span class="pfb-validation-icon dashicons dashicons-yes-alt"></span>
                                    <span class="pfb-validation-text">Conditional logic: <strong id="pfb-popup-conditional-count"></strong></span>
                                </div>
                            </div>
                            <div class="pfb-save-progress" style="display: none;">
                                <div class="pfb-progress-bar">
                                    <div class="pfb-progress-fill"></div>
                                </div>
                                <p class="pfb-progress-text">Saving form...</p>
                            </div>
                            <div class="pfb-save-result" style="display: none;">
                                <div class="pfb-result-icon"></div>
                                <p class="pfb-result-text"></p>
                            </div>
                        </div>
                        <div class="pfb-popup-footer">
                            <button class="pfb-btn pfb-btn-secondary pfb-popup-cancel">Cancel</button>
                            <button class="pfb-btn pfb-btn-primary pfb-popup-save">
                                <span class="dashicons dashicons-saved"></span>
                                Save Form
                            </button>
                        </div>
                    </div>
                </div>
            `;

            console.log('PFB Admin: Adding popup to page');

            // Add popup to page
            $('body').append(popupHtml);

            // Populate validation data
            const formTitle = $('#form_title').val() || 'Untitled Form';
            const fieldsCount = $('.pfb-form-fields .pfb-field').length;
            const conditionalCount = $('.pfb-enable-conditional-logic:checked').length;

            console.log('PFB Admin: Popup data - Title:', formTitle, 'Fields:', fieldsCount, 'Conditional:', conditionalCount);

            $('#pfb-popup-title').text(formTitle);
            $('#pfb-popup-fields-count').text(fieldsCount + ' fields');
            $('#pfb-popup-conditional-count').text(conditionalCount + ' fields with conditions');

            console.log('PFB Admin: Showing popup with animation');

            // Show popup with animation
            $('.pfb-save-popup-overlay').fadeIn(300);
            setTimeout(function() {
                $('.pfb-save-popup').addClass('pfb-popup-show');
            }, 50);

            // Bind events
            $('.pfb-popup-close, .pfb-popup-cancel').on('click', closeSavePopup);
            $('.pfb-popup-save').on('click', function() {
                console.log('PFB Admin: Save button in popup clicked');
                $(this).prop('disabled', true);
                $('.pfb-save-validation').hide();
                $('.pfb-popup-footer').hide();
                $('.pfb-save-progress').show();

                // Animate progress bar
                $('.pfb-progress-fill').animate({width: '100%'}, 2000);

                // Save form
                saveForm();
            });

            console.log('PFB Admin: Popup setup complete');

        } catch (error) {
            console.error('PFB Admin: Error in showSavePopup:', error);
            throw error;
        }
    }

    /**
     * Close save popup.
     */
    function closeSavePopup() {
        $('.pfb-save-popup').removeClass('pfb-popup-show');
        setTimeout(function() {
            $('.pfb-save-popup-overlay').remove();
        }, 300);
    }

    /**
     * Show save result in popup.
     */
    function showSaveResult(success, message) {
        $('.pfb-save-progress').hide();
        $('.pfb-save-result').show();

        if (success) {
            $('.pfb-result-icon').html('<span class="dashicons dashicons-yes-alt"></span>').addClass('pfb-success');
            $('.pfb-result-text').text('Form saved successfully!');

            setTimeout(function() {
                closeSavePopup();
            }, 2000);
        } else {
            $('.pfb-result-icon').html('<span class="dashicons dashicons-warning"></span>').addClass('pfb-error');
            $('.pfb-result-text').text(message || 'Failed to save form');

            // Show retry button
            $('.pfb-popup-footer').show().html(`
                <button class="pfb-btn pfb-btn-secondary" onclick="closeSavePopup()">Close</button>
                <button class="pfb-btn pfb-btn-primary" onclick="location.reload()">Retry</button>
            `);
        }
    }

    /**
     * Save form data.
     */
    function saveForm() {
        const $form = $('#pfb-form-editor-form');
        const formData = {
            title: $('#form_title').val(),
            description: $('#form_description').val(),
            status: $('#form_status').val(),
            settings: {
                template: $('#form_template').val(),
                show_currency_selector: $('#form_show_currency_selector').val(),
                submit_button_text: $('#form_submit_button_text').val()
            },
            fields: []
        };

        // Debug log for form settings
        console.log('Saving form with template:', formData.settings.template);

        // Get form ID if editing
        const urlParams = new URLSearchParams(window.location.search);
        const formId = urlParams.get('form_id');

        if (formId) {
            formData.id = formId;
        }

        // Get fields
        $('.pfb-form-fields .pfb-field').each(function(index) {
            const $field = $(this);
            const fieldType = $field.data('type');
            const fieldId = $field.attr('id');

            // Get field width from dropdown or data attribute
            const $widthDropdown = $field.find('.pfb-field-width-input');
            const dropdownWidth = $widthDropdown.length > 0 ? $widthDropdown.val() : null;
            const dataWidth = $field.attr('data-width');

            // Get the numeric width value (without % symbol)
            let fieldWidth = dropdownWidth || dataWidth || '100';

            // Remove % symbol if present for storage
            fieldWidth = fieldWidth.toString().replace('%', '');



            const field = {
                type: fieldType,
                label: $field.find('.pfb-field-label-input').val(),
                name: $field.find('.pfb-field-name-input').val(),
                required: $field.find('.pfb-field-required-input').is(':checked'),
                hidden: fieldType === 'text' ? $field.find('.pfb-field-hidden-input').is(':checked') : false,
                width: fieldWidth,
                options: {}
            };



            // Add hidden field properties directly to the field object
            if (fieldType === 'text' && field.hidden) {
                // Get variable and value
                const variableValue = $field.find('.pfb-hidden-field-variable').val();
                const defaultValue = $field.find('.pfb-hidden-field-value').val();

                // Store variable and default value directly on the field object
                if (variableValue) {
                    field.variable = variableValue;
                }

                if (defaultValue) {
                    field.default_value = defaultValue;
                }

                // Log for debugging
                console.log('Hidden field data for ' + field.name + ':', {
                    hidden: field.hidden,
                    variable: field.variable,
                    default_value: field.default_value
                });

                // Also log the DOM elements for debugging
                console.log('Hidden field DOM elements:', {
                    hidden_checkbox: $field.find('.pfb-field-hidden-input').get(0),
                    variable_input: $field.find('.pfb-hidden-field-variable').get(0),
                    value_input: $field.find('.pfb-hidden-field-value').get(0)
                });
            }

            // Get field-specific options
            switch (fieldType) {
                case 'slider':
                    // Get slider options
                    const min = $field.find('.pfb-slider-min-input').val() || 0;
                    const max = $field.find('.pfb-slider-max-input').val() || 100;
                    const step = $field.find('.pfb-slider-step-input').val() || 1;
                    const defaultValue = $field.find('.pfb-slider-default-input').val() || 50;

                    field.options.min = parseInt(min);
                    field.options.max = parseInt(max);
                    field.options.step = parseInt(step);
                    field.options.default = parseInt(defaultValue);

                    // Log options for debugging
                    console.log(`Slider field ${fieldId} options:`, JSON.parse(JSON.stringify(field.options)));
                    break;

                case 'subtotal':
                    // Get subtotal lines
                    const lines = [];
                    $field.find('.pfb-subtotal-line').each(function() {
                        const $line = $(this);
                        const label = $line.find('.pfb-subtotal-line-label').val();
                        const formula = $line.find('.pfb-subtotal-line-formula').val();

                        console.log(`Subtotal line: label=${label}, formula=${formula}`);

                        // Only add lines that have either a label or a formula
                        // This prevents empty lines from being saved
                        if (label || formula) {
                            lines.push({
                                label: label || 'Line',
                                formula: formula || ''
                            });
                        }
                    });

                    // Make sure we have at least one line
                    if (lines.length === 0) {
                        lines.push({
                            label: 'Line 1',
                            formula: ''
                        });
                    }

                    // Get empty value display
                    const emptyValue = $field.find('.pfb-subtotal-empty-value-input').val() || '---';

                    // Make sure we're creating a new object for options
                    field.options = field.options || {};
                    field.options.lines = lines;
                    field.options.empty_value = emptyValue;

                    // Log options for debugging
                    console.log(`Subtotal field ${fieldId} options:`, JSON.parse(JSON.stringify(field.options)));
                    break;

                case 'dropdown':
                case 'radio':
                case 'checkbox':
                    // Use the getFieldOptions function to get all options
                    const fieldOptions = getFieldOptions($field);

                    // Filter out empty options
                    field.options.items = fieldOptions.filter(option =>
                        option.label !== '' || option.value !== ''
                    );

                    // Ensure each option has all required properties
                    field.options.items = field.options.items.map(option => ({
                        label: option.label || 'Option',
                        value: option.value || option.label || '0',
                        variable: option.variable || ''
                    }));

                    // Log options for debugging
                    console.log(`Field ${fieldId} options:`, JSON.parse(JSON.stringify(field.options.items)));

                    field.options.variable = $field.find('.pfb-field-variable-input').val();
                    break;

                case 'total':
                    const formula = $field.find('.pfb-field-formula-input').val();

                    // Validate formula before saving
                    if (formula && !validateFormula(formula)) {
                        showFormulaWarning($field, 'Formula may have syntax errors. Please check parentheses and operators.');

                        // Scroll to the field with the error
                        $('html, body').animate({
                            scrollTop: $field.offset().top - 100
                        }, 300);

                        // Continue saving anyway, but with a warning
                        console.warn('Saving formula with potential syntax errors:', formula);
                    }

                    field.options.formula = formula;
                    break;
            }

            // Get conditional logic data - CLEAN NEW IMPLEMENTATION
            const conditionalLogic = getFieldConditionalLogic($field);

            if (conditionalLogic) {
                // Store conditional logic as a single JSON object
                field.conditional_logic = conditionalLogic;
                console.log(`Field ${field.name} has conditional logic:`, conditionalLogic);
            } else {
                field.conditional_logic = null;
                console.log(`Field ${field.name} has no conditional logic`);
            }

            formData.fields.push(field);
        });

        // Debug form data before saving
        logFormData(formData);



        // Debug log for form data before AJAX
        console.log('Form data being sent to server:', JSON.stringify(formData));
        console.log('Template setting:', formData.settings.template);

        // Save form via AJAX
        $.ajax({
            url: pfb_data.ajax_url,
            type: 'POST',
            data: {
                action: 'pfb_save_form',
                nonce: pfb_data.nonce,
                form_data: formData
            },
            beforeSend: function() {
                $('#pfb-save-form').prop('disabled', true).text(pfb_data.i18n.loading);
            },
            success: function(response) {
                console.log('PFB Admin: AJAX response received:', response);

                if (response.success) {
                    // Show success message
                    alert('Form saved successfully!');
                    console.log('PFB Admin: Form saved successfully');

                    // Redirect to edit page if new form
                    if (!formId && response.data.form_id) {
                        console.log('PFB Admin: Redirecting to edit page for new form ID:', response.data.form_id);
                        window.location.href = 'admin.php?page=pfb-form-editor&form_id=' + response.data.form_id;
                    }
                } else {
                    console.error('PFB Admin: Save failed:', response.data.message);
                    alert('Failed to save form: ' + (response.data.message || 'Unknown error'));
                }
            },
            error: function(xhr, status, error) {
                console.error('PFB Admin: AJAX error:', status, error);
                alert('An error occurred while saving the form: ' + error);
            },
            complete: function() {
                $('#pfb-save-form').prop('disabled', false).text(pfb_data.i18n.save);
            }
        });
    }

    /**
     * Load form data for editing.
     */
    function loadForm(formId) {
        console.log('LOAD FORM: Loading form with ID:', formId);

        $.ajax({
            url: pfb_data.ajax_url,
            type: 'GET',
            data: {
                action: 'pfb_get_form',
                nonce: pfb_data.nonce,
                form_id: formId
            },
            beforeSend: function() {
                $('#pfb-form-editor-form').addClass('loading');
                console.log('LOAD FORM: Sending AJAX request to load form');
            },
            success: function(response) {
                console.log('LOAD FORM: Received response:', response);

                if (response.success && response.data.form) {
                    const form = response.data.form;
                    console.log('LOAD FORM: Form data:', form);

                    // Set form data
                    $('#form_title').val(form.title);
                    $('#form_description').val(form.description);
                    $('#form_status').val(form.status);

                    // Load form settings if available
                    if (form.settings) {
                        if (form.settings.template) {
                            $('#form_template').val(form.settings.template);
                        }
                        if (form.settings.show_currency_selector !== undefined) {
                            $('#form_show_currency_selector').val(form.settings.show_currency_selector);
                        }
                        if (form.settings.submit_button_text) {
                            $('#form_submit_button_text').val(form.settings.submit_button_text);
                        }
                    }

                    // Clear existing fields
                    $('.pfb-form-fields').empty();

                    // Add fields
                    if (form.fields && form.fields.length > 0) {
                        form.fields.forEach(function(field) {
                            const fieldId = 'field_' + Date.now() + '_' + Math.floor(Math.random() * 1000);
                            const $field = createField(field.field_type, fieldId);

                            // Set field data
                            $field.find('.pfb-field-label-input').val(field.field_label);
                            $field.find('.pfb-field-name-input').val(field.field_name);
                            $field.find('.pfb-field-required-input').prop('checked', field.field_required == 1);

                            // Set field width if available
                            let fieldWidth = '100';



                            // First try to get width from field_width column (new approach)
                            if (field.field_width !== undefined && field.field_width !== null) {
                                // Get the numeric width value
                                fieldWidth = field.field_width.toString().replace('%', '');
                            }
                            // Fallback to field_options.width (old approach)
                            else if (field.field_options && field.field_options.width) {
                                // Get the numeric width value
                                fieldWidth = field.field_options.width.toString().replace('%', '');
                            }

                            // Set the width in the dropdown and as a data attribute
                            const $widthDropdown = $field.find('.pfb-field-width-input');


                            if ($widthDropdown.length > 0) {
                                // Force the dropdown to have the correct value
                                $widthDropdown.val(fieldWidth);

                                // If the value wasn't set correctly, try to find the option and select it
                                if ($widthDropdown.val() !== fieldWidth) {
                                    // Try to find the option with the correct value
                                    const $option = $widthDropdown.find('option[value="' + fieldWidth + '"]');

                                    if ($option.length > 0) {
                                        // Select the option
                                        $option.prop('selected', true);
                                    } else {
                                        // Add a new option if it doesn't exist
                                        $widthDropdown.append('<option value="' + fieldWidth + '">' + fieldWidth + '% (Custom)</option>');
                                        $widthDropdown.val(fieldWidth);
                                    }
                                }
                            }

                            // Store the width as a data attribute (without % symbol)
                            $field.attr('data-width', fieldWidth);

                            // Add width indicator if not 100% (with % symbol for display)
                            if (fieldWidth !== '100') {
                                $field.find('.pfb-field-title').after(`<span class="pfb-field-width-indicator">Width: ${fieldWidth}%</span>`);
                            }

                            // Update the field title with the label
                            $field.find('.pfb-field-title').text(field.field_label);

                            // Get field options
                            let fieldOptions = {};
                            if (field.field_options) {
                                // Handle field options
                                fieldOptions = field.field_options || {};

                                // For subtotal fields, ensure lines array exists
                                if (field.field_type === 'subtotal') {
                                    console.log('Processing subtotal field options:', fieldOptions);

                                    if (!fieldOptions.lines || !Array.isArray(fieldOptions.lines)) {
                                        console.log('Creating default lines array for subtotal field');
                                        fieldOptions.lines = [
                                            {
                                                label: 'Line 1',
                                                formula: ''
                                            }
                                        ];
                                    }

                                    if (!fieldOptions.empty_value) {
                                        fieldOptions.empty_value = '---';
                                    }
                                }

                                // Debug log
                                console.log('Field options loaded for field ' + field.field_name + ':', fieldOptions);
                            }

                            // Check if this is a hidden text field
                            let isHidden = false;

                            if (field.field_type === 'text') {
                                // Check all possible ways the field could be marked as hidden
                                isHidden = (field.field_hidden == 1) ||
                                          (fieldOptions && fieldOptions.is_hidden === true);

                                console.log('Field ' + field.field_name + ' hidden status check:', {
                                    isHidden: isHidden,
                                    field_hidden: field.field_hidden,
                                    options_is_hidden: fieldOptions && fieldOptions.is_hidden
                                });
                            }

                            if (isHidden) {
                                // Set the hidden checkbox
                                $field.find('.pfb-field-hidden-input').prop('checked', true);

                                // Show the hidden field options
                                $field.find('.pfb-hidden-field-options').show();

                                console.log('Setting up hidden field ' + field.field_name);

                                // Get variable from all possible sources
                                let variableValue = null;
                                if (field.field_variable) {
                                    variableValue = field.field_variable;
                                    console.log('Found variable in field_variable:', variableValue);
                                } else if (fieldOptions && fieldOptions.variable) {
                                    variableValue = fieldOptions.variable;
                                    console.log('Found variable in fieldOptions.variable:', variableValue);
                                }

                                // Get default value from all possible sources
                                let defaultValue = null;
                                if (field.field_default_value) {
                                    defaultValue = field.field_default_value;
                                    console.log('Found default_value in field_default_value:', defaultValue);
                                } else if (fieldOptions && fieldOptions.default_value) {
                                    defaultValue = fieldOptions.default_value;
                                    console.log('Found default_value in fieldOptions.default_value:', defaultValue);
                                }

                                // Set the values in the form
                                if (variableValue) {
                                    $field.find('.pfb-hidden-field-variable').val(variableValue);
                                    $field.find('.pfb-hidden-field-value').val('{' + variableValue + '}');
                                } else if (defaultValue) {
                                    $field.find('.pfb-hidden-field-value').val(defaultValue);
                                }

                                // Log the final values
                                console.log('Final hidden field values for ' + field.field_name + ':', {
                                    variable: $field.find('.pfb-hidden-field-variable').val(),
                                    value: $field.find('.pfb-hidden-field-value').val()
                                });
                            }

                            // Set field-specific options
                            const options = field.field_options;

                            if (options) {
                                switch (field.field_type) {
                                    case 'slider':
                                        // Log the options for debugging
                                        console.log('Loading slider field options:', options);

                                        // Set slider options
                                        const min = options.min !== undefined ? options.min : 0;
                                        const max = options.max !== undefined ? options.max : 100;
                                        const step = options.step !== undefined ? options.step : 1;
                                        const defaultValue = options.default !== undefined ? options.default : 50;

                                        $field.find('.pfb-slider-min-input').val(min);
                                        $field.find('.pfb-slider-max-input').val(max);
                                        $field.find('.pfb-slider-step-input').val(step);
                                        $field.find('.pfb-slider-default-input').val(defaultValue);

                                        console.log('Set slider options:', { min, max, step, default: defaultValue });
                                        break;

                                    case 'dropdown':
                                    case 'radio':
                                    case 'checkbox':
                                        // Log the options for debugging
                                        console.log('Loading field options:', options);

                                        // Ensure options.items is an array
                                        if (!options.items || !Array.isArray(options.items)) {
                                            options.items = [];
                                            console.log('Field options.items was not an array, initializing empty array');
                                        }

                                        // Add a default option if none exist
                                        if (options.items.length === 0) {
                                            options.items.push({
                                                label: 'Option 1',
                                                value: 'option_1',
                                                variable: ''
                                            });
                                            console.log('Added default option to empty options list');
                                        }

                                        if (options.items && options.items.length > 0) {
                                            const $optionsContainer = $field.find('.pfb-field-options');
                                            $optionsContainer.empty();

                                            options.items.forEach(function(item, index) {
                                                const $option = $('<div class="pfb-field-option">');

                                                // Ensure we have valid values
                                                const label = item.label || 'Option';
                                                const value = item.value || label || '0';
                                                const variable = item.variable || '';

                                                console.log(`Option ${index}: label=${label}, value=${value}, variable=${variable}`);

                                                $option.append('<input type="text" class="pfb-form-control pfb-option-label" placeholder="Label" value="' + label + '">');

                                                // Create value container with dynamic value button
                                                const $valueContainer = $('<div class="pfb-option-value-container">');
                                                $valueContainer.append('<input type="text" class="pfb-form-control pfb-option-value" placeholder="Value" value="' + value + '">');
                                                $valueContainer.append('<button type="button" class="pfb-dynamic-value-button pfb-select-dynamic-value"><span class="dashicons dashicons-database"></span></button>');
                                                $valueContainer.append('<input type="hidden" class="pfb-option-variable" value="' + variable + '">');

                                                $option.append($valueContainer);

                                                // If the value is a variable placeholder, update the value input
                                                if (variable) {
                                                    $valueContainer.find('.pfb-option-value').val('{' + variable + '}');
                                                }

                                                if (index === 0) {
                                                    $option.append('<button type="button" class="pfb-btn pfb-btn-secondary pfb-add-option">+</button>');
                                                } else {
                                                    $option.append('<button type="button" class="pfb-btn pfb-btn-secondary pfb-remove-option">-</button>');
                                                }

                                                $optionsContainer.append($option);
                                            });
                                        }

                                        if (options.variable) {
                                            $field.find('.pfb-field-variable-input').val(options.variable);
                                        }
                                        break;

                                    case 'subtotal':
                                        console.log('Loading subtotal field options:', options);

                                        // Set empty value display
                                        if (options.empty_value) {
                                            $field.find('.pfb-subtotal-empty-value-input').val(options.empty_value);
                                        }

                                        // Load subtotal lines
                                        if (options.lines && options.lines.length > 0) {
                                            console.log('Loading subtotal lines:', options.lines);

                                            // Clear existing lines
                                            const $linesContainer = $field.find('.pfb-subtotal-lines');
                                            $linesContainer.empty();

                                            // Add each line
                                            options.lines.forEach(function(line, index) {
                                                console.log(`Loading subtotal line ${index}:`, line);

                                                const $line = $('<div class="pfb-subtotal-line">');

                                                // Add label input
                                                $line.append('<input type="text" class="pfb-form-control pfb-subtotal-line-label" placeholder="Line Label" value="' + (line.label || 'Line ' + (index + 1)) + '">');

                                                // Add formula input container
                                                const $formulaContainer = $('<div class="pfb-formula-input-container">');
                                                $formulaContainer.append('<textarea class="pfb-form-control pfb-subtotal-line-formula" rows="2" placeholder="Formula">' + (line.formula || '') + '</textarea>');
                                                $formulaContainer.append('<button type="button" class="pfb-dynamic-value-button pfb-select-subtotal-formula"><span class="dashicons dashicons-editor-code"></span></button>');
                                                $line.append($formulaContainer);

                                                // Add add/remove button
                                                if (index === 0) {
                                                    $line.append('<button type="button" class="pfb-btn pfb-btn-secondary pfb-add-subtotal-line">+</button>');
                                                } else {
                                                    $line.append('<button type="button" class="pfb-btn pfb-btn-secondary pfb-remove-subtotal-line">-</button>');
                                                }

                                                $linesContainer.append($line);
                                            });
                                        } else {
                                            console.log('No subtotal lines found, using default');

                                            // If no lines are defined, make sure we have at least one default line
                                            const $linesContainer = $field.find('.pfb-subtotal-lines');

                                            // Check if there's already a line
                                            if ($linesContainer.find('.pfb-subtotal-line').length === 0) {
                                                console.log('Adding default subtotal line');

                                                const $line = $('<div class="pfb-subtotal-line">');

                                                // Add label input
                                                $line.append('<input type="text" class="pfb-form-control pfb-subtotal-line-label" placeholder="Line Label" value="Line 1">');

                                                // Add formula input container
                                                const $formulaContainer = $('<div class="pfb-formula-input-container">');
                                                $formulaContainer.append('<textarea class="pfb-form-control pfb-subtotal-line-formula" rows="2" placeholder="Formula"></textarea>');
                                                $formulaContainer.append('<button type="button" class="pfb-dynamic-value-button pfb-select-subtotal-formula"><span class="dashicons dashicons-editor-code"></span></button>');
                                                $line.append($formulaContainer);

                                                // Add add button
                                                $line.append('<button type="button" class="pfb-btn pfb-btn-secondary pfb-add-subtotal-line">+</button>');

                                                $linesContainer.append($line);
                                            }
                                        }
                                        break;

                                    case 'total':
                                        if (options.formula) {
                                            $field.find('.pfb-field-formula-input').val(options.formula);
                                        }
                                        break;
                                }
                            }

                            // Load conditional logic if available
                            if (field.conditional_logic || field.conditions) {
                                console.log('Loading conditional logic for field:', field.field_name);

                                // Enable conditional logic checkbox
                                $field.find('.pfb-enable-conditional-logic').prop('checked', true);

                                // Show conditional logic settings
                                $field.find('.pfb-conditional-logic-settings').show();

                                // Set action and operator
                                if (field.conditional_action) {
                                    $field.find('.pfb-conditional-action').val(field.conditional_action);
                                }
                                if (field.conditional_operator) {
                                    $field.find('.pfb-conditional-operator').val(field.conditional_operator);
                                }

                                // Load conditions
                                if (field.conditions && field.conditions.length > 0) {
                                    const $conditionsContainer = $field.find('.pfb-conditional-conditions');
                                    $conditionsContainer.empty();

                                    field.conditions.forEach(function(condition, index) {
                                        const $condition = $('<div class="pfb-condition-row">');

                                        $condition.html(`
                                            <div class="pfb-condition-inputs">
                                                <select class="pfb-form-control pfb-condition-field">
                                                    <option value="">Select Field</option>
                                                </select>
                                                <select class="pfb-form-control pfb-condition-operator">
                                                    <option value="equals">Equals</option>
                                                    <option value="not_equals">Not Equals</option>
                                                    <option value="greater_than">Greater Than</option>
                                                    <option value="less_than">Less Than</option>
                                                    <option value="contains">Contains</option>
                                                    <option value="is_empty">Is Empty</option>
                                                    <option value="is_not_empty">Is Not Empty</option>
                                                </select>
                                                <input type="text" class="pfb-form-control pfb-condition-value" placeholder="Value">
                                                <button type="button" class="pfb-btn pfb-btn-danger pfb-remove-condition">
                                                    <span class="dashicons dashicons-trash"></span>
                                                </button>
                                            </div>
                                        `);

                                        // Store condition data for later setting (after fields are loaded)
                                        $condition.data('condition-data', condition);

                                        $conditionsContainer.append($condition);
                                    });

                                    // Load available fields for conditions FIRST
                                    loadConditionalFields($field);

                                    // THEN set the condition values after fields are loaded
                                    $conditionsContainer.find('.pfb-condition-row').each(function() {
                                        const $condition = $(this);
                                        const conditionData = $condition.data('condition-data');

                                        if (conditionData) {
                                            $condition.find('.pfb-condition-field').val(conditionData.field);
                                            $condition.find('.pfb-condition-operator').val(conditionData.operator);
                                            $condition.find('.pfb-condition-value').val(conditionData.value);

                                            console.log('Set condition values for field:', conditionData.field, 'operator:', conditionData.operator, 'value:', conditionData.value);
                                        }
                                    });
                                }

                                // Add conditional indicator
                                $field.find('.pfb-field-title').after('<span class="pfb-field-conditional-indicator">Conditional</span>');
                            }

                            $('.pfb-form-fields').append($field);
                        });
                    }
                } else {
                    showNotification('error', 'Failed to load form data.');
                }
            },
            error: function() {
                showNotification('error', 'An error occurred while loading the form.');
            },
            complete: function() {
                $('#pfb-form-editor-form').removeClass('loading');
            }
        });
    }

    /**
     * Initialize price variables functionality.
     */
    function initPriceVariables() {
        // Implementation will be added in the next phase
    }

    /**
     * Initialize currencies functionality.
     */
    function initCurrencies() {
        // Implementation will be added in the next phase
    }

    /**
     * Initialize settings functionality.
     */
    function initSettings() {
        // Implementation will be added in the next phase
    }

    /**
     * Add a condition row to the conditional logic settings.
     */
    function addCondition($field) {
        const conditionHtml = `
            <div class="pfb-condition-row">
                <div class="pfb-condition-inputs">
                    <select class="pfb-form-control pfb-condition-field">
                        <option value="">Select Field</option>
                    </select>
                    <select class="pfb-form-control pfb-condition-operator">
                        <option value="equals">Equals</option>
                        <option value="not_equals">Not Equals</option>
                        <option value="greater_than">Greater Than</option>
                        <option value="less_than">Less Than</option>
                        <option value="contains">Contains</option>
                        <option value="is_empty">Is Empty</option>
                        <option value="is_not_empty">Is Not Empty</option>
                    </select>
                    <input type="text" class="pfb-form-control pfb-condition-value" placeholder="Value">
                    <button type="button" class="pfb-btn pfb-btn-danger pfb-remove-condition">
                        <span class="dashicons dashicons-trash"></span>
                    </button>
                </div>
            </div>
        `;

        $field.find('.pfb-conditional-conditions').append(conditionHtml);
    }

    /**
     * Load available fields for conditional logic.
     */
    function loadConditionalFields($field) {
        const currentFieldName = $field.find('.pfb-field-name-input').val();

        // Get all form fields except the current one
        const $fieldSelects = $field.find('.pfb-condition-field');

        $fieldSelects.each(function() {
            const $select = $(this);
            $select.find('option:not(:first)').remove(); // Keep "Select Field" option

            $('.pfb-form-fields .pfb-field').each(function() {
                const $otherField = $(this);
                const fieldName = $otherField.find('.pfb-field-name-input').val();
                const fieldLabel = $otherField.find('.pfb-field-label-input').val();

                // Don't include the current field
                if (fieldName && fieldName !== currentFieldName) {
                    $select.append(`<option value="${fieldName}">${fieldLabel} (${fieldName})</option>`);
                }
            });
        });
    }

    /**
     * Get conditional logic data from field settings - CLEAN NEW IMPLEMENTATION.
     */
    function getFieldConditionalLogic($field) {
        const hasConditionalLogic = $field.find('.pfb-enable-conditional-logic').is(':checked');

        if (!hasConditionalLogic) {
            return null;
        }

        const action = $field.find('.pfb-conditional-action').val() || 'show';
        const operator = $field.find('.pfb-conditional-operator').val() || 'and';
        const conditions = [];

        // Get conditions from the new structure
        $field.find('.pfb-condition-row').each(function() {
            const $row = $(this);
            const field = $row.find('.pfb-condition-field').val();
            const conditionOperator = $row.find('.pfb-condition-operator').val();
            const value = $row.find('.pfb-condition-value').val();

            if (field && conditionOperator) {
                conditions.push({
                    field: field,
                    operator: conditionOperator,
                    value: value || ''
                });
            }
        });

        if (conditions.length === 0) {
            return null;
        }

        return {
            action: action,
            operator: operator,
            conditions: conditions
        };
    }

    /**
     * Get field options from a field element
     */
    function getFieldOptions($field) {
        const options = [];

        $field.find('.pfb-field-option').each(function() {
            const $option = $(this);
            const label = $option.find('.pfb-option-label').val();
            const value = $option.find('.pfb-option-value').val();
            const variable = $option.find('.pfb-option-variable').val();

            options.push({
                label: label || 'Option',
                value: value || '0',
                variable: variable || ''
            });
        });

        return options;
    }

    /**
     * Validate a formula for syntax errors.
     */
    function validateFormula(formula) {
        // Empty formula is valid (will return 0)
        if (!formula.trim()) {
            return true;
        }

        // Check for balanced parentheses
        const openCount = (formula.match(/\(/g) || []).length;
        const closeCount = (formula.match(/\)/g) || []).length;

        if (openCount !== closeCount) {
            console.warn('Unbalanced parentheses in formula:', formula);
            return false;
        }

        // Check for proper nesting of parentheses
        try {
            let level = 0;
            for (let i = 0; i < formula.length; i++) {
                if (formula[i] === '(') {
                    level++;
                } else if (formula[i] === ')') {
                    level--;
                    if (level < 0) {
                        console.warn('Improper nesting of parentheses in formula:', formula);
                        return false;
                    }
                }
            }
        } catch (e) {
            console.error('Error validating formula:', e);
            return false;
        }

        // Check for proper function syntax
        const functionPattern = /\b(ceil|floor|round|min|max)\s*\(/g;
        let match;
        while ((match = functionPattern.exec(formula)) !== null) {
            const funcPos = match.index;
            const openParenPos = funcPos + match[0].length - 1;

            // Find the matching closing parenthesis
            let level = 1;
            let closeParenPos = -1;

            for (let i = openParenPos + 1; i < formula.length; i++) {
                if (formula[i] === '(') {
                    level++;
                } else if (formula[i] === ')') {
                    level--;
                    if (level === 0) {
                        closeParenPos = i;
                        break;
                    }
                }
            }

            if (closeParenPos === -1) {
                console.warn('Missing closing parenthesis for function in formula:', formula);
                return false;
            }
        }

        // The following checks are warnings but not errors

        // Check for consecutive operators
        if (/[\+\-\*\/]{2,}/.test(formula)) {
            console.warn('Warning: Consecutive operators in formula:', formula);
        }

        // Check for empty parentheses
        if (/\(\s*\)/.test(formula)) {
            console.warn('Warning: Empty parentheses in formula:', formula);
        }

        // Check for missing operands
        if (/[\+\-\*\/]\s*$/.test(formula)) {
            console.warn('Warning: Missing operand at the end of formula:', formula);
        }

        // Check for function calls without arguments
        if (/\b(ceil|floor|round|min|max)\s*\(\s*\)/.test(formula)) {
            console.warn('Warning: Function call without arguments in formula:', formula);
        }

        return true;
    }

    /**
     * Show a warning message in the formula builder.
     */
    function showFormulaWarning($field, message) {
        // Check if warning element already exists
        let $warning = $field.find('.pfb-formula-message');

        if (!$warning.length) {
            // Create warning element
            $warning = $('<div class="pfb-formula-message pfb-formula-warning"></div>');
            $field.find('.pfb-formula-input-container').append($warning);
        } else {
            // Make sure it has the warning class
            $warning.removeClass('pfb-formula-info').addClass('pfb-formula-warning');
        }

        // Set message and show
        $warning.text(message).fadeIn();

        // Hide after 3 seconds
        setTimeout(function() {
            $warning.fadeOut();
        }, 3000);
    }

    /**
     * Show an informational message in the formula builder.
     */
    function showFormulaInfo($field, message) {
        // Check if info element already exists
        let $info = $field.find('.pfb-formula-message');

        if (!$info.length) {
            // Create info element
            $info = $('<div class="pfb-formula-message pfb-formula-info"></div>');
            $field.find('.pfb-formula-input-container').append($info);
        } else {
            // Make sure it has the info class
            $info.removeClass('pfb-formula-warning').addClass('pfb-formula-info');
        }

        // Set message and show
        $info.text(message).fadeIn();

        // Hide after 5 seconds
        setTimeout(function() {
            $info.fadeOut();
        }, 5000);
    }

    /**
     * Update field preview based on the options.
     */
    function updateFieldPreview($field) {
        const fieldType = $field.data('type');
        const $preview = $field.find('.pfb-field-preview');

        // Get all options
        const options = [];
        $field.find('.pfb-field-option').each(function() {
            const label = $(this).find('.pfb-option-label').val() || 'Option';
            options.push(label);
        });

        if (options.length === 0) {
            return;
        }

        // Create preview HTML based on field type
        let previewHtml = '';

        switch (fieldType) {
            case 'dropdown':
                previewHtml = '<select class="pfb-form-control" disabled>';
                options.forEach(function(label) {
                    previewHtml += '<option>' + label + '</option>';
                });
                previewHtml += '</select>';
                break;

            case 'radio':
                previewHtml = '<div>';
                options.forEach(function(label) {
                    previewHtml += '<label><input type="radio" name="radio_preview" disabled> ' + label + '</label><br>';
                });
                previewHtml += '</div>';
                break;

            case 'checkbox':
                previewHtml = '<div>';
                options.forEach(function(label) {
                    previewHtml += '<label><input type="checkbox" disabled> ' + label + '</label><br>';
                });
                previewHtml += '</div>';
                break;
        }

        // Update preview
        if (previewHtml) {
            $preview.html(previewHtml);
        }
    }

    /**
     * Show notification message.
     */
    function showNotification(type, message) {
        const $notification = $('<div class="pfb-notification pfb-notification-' + type + '">' + message + '</div>');

        $('.pfb-admin-content').prepend($notification);

        setTimeout(function() {
            $notification.fadeOut(function() {
                $(this).remove();
            });
        }, 5000);
    }

    // Test jQuery availability
    console.log('PFB Admin: jQuery available:', typeof $ !== 'undefined');
    console.log('PFB Admin: Document ready state:', document.readyState);

    // Initialize when document is ready
    $(document).ready(function() {
        console.log('PFB Admin: Document ready fired');
        init();
    });

})(jQuery);
